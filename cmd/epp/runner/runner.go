/*
Copyright 2025 The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package runner

import (
	"context"
	"flag"
	"fmt"
	"net/http/pprof"
	"os"

	"github.com/go-logr/logr"
	"github.com/prometheus/client_golang/prometheus"
	uberzap "go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"google.golang.org/grpc"
	healthPb "google.golang.org/grpc/health/grpc_health_v1"
	"k8s.io/apimachinery/pkg/types"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/log"
	"sigs.k8s.io/controller-runtime/pkg/log/zap"
	"sigs.k8s.io/controller-runtime/pkg/manager"
	"sigs.k8s.io/controller-runtime/pkg/metrics/filters"
	metricsserver "sigs.k8s.io/controller-runtime/pkg/metrics/server"

	"sigs.k8s.io/gateway-api-inference-extension/internal/runnable"
	backendmetrics "sigs.k8s.io/gateway-api-inference-extension/pkg/epp/backend/metrics"
	"sigs.k8s.io/gateway-api-inference-extension/pkg/epp/common/config/loader"
	"sigs.k8s.io/gateway-api-inference-extension/pkg/epp/datastore"
	"sigs.k8s.io/gateway-api-inference-extension/pkg/epp/metrics"
	"sigs.k8s.io/gateway-api-inference-extension/pkg/epp/metrics/collectors"
	"sigs.k8s.io/gateway-api-inference-extension/pkg/epp/requestcontrol"
	"sigs.k8s.io/gateway-api-inference-extension/pkg/epp/saturationdetector"
	"sigs.k8s.io/gateway-api-inference-extension/pkg/epp/scheduling"
	runserver "sigs.k8s.io/gateway-api-inference-extension/pkg/epp/server"
	"sigs.k8s.io/gateway-api-inference-extension/pkg/epp/util/logging"
)

var (
	grpcPort = flag.Int(
		"grpcPort",
		runserver.DefaultGrpcPort,
		"The gRPC port used for communicating with Envoy proxy")
	grpcHealthPort = flag.Int(
		"grpcHealthPort",
		runserver.DefaultGrpcHealthPort,
		"The port used for gRPC liveness and readiness probes")
	metricsPort = flag.Int(
		"metricsPort",
		runserver.DefaultMetricsPort,
		"The metrics port")
	enablePprof = flag.Bool(
		"enablePprof",
		runserver.DefaultEnablePprof,
		"Enables pprof handlers. Defaults to true. Set to false to disable pprof handlers.")
	destinationEndpointHintKey = flag.String(
		"destinationEndpointHintKey",
		runserver.DefaultDestinationEndpointHintKey,
		"Header and response metadata key used by Envoy to route to the appropriate pod. This must match Envoy configuration.")
	destinationEndpointHintMetadataNamespace = flag.String(
		"DestinationEndpointHintMetadataNamespace",
		runserver.DefaultDestinationEndpointHintMetadataNamespace,
		"The key for the outer namespace struct in the metadata field of the extproc response that is used to wrap the"+
			"target endpoint. If not set, then an outer namespace struct should not be created.")
	poolName = flag.String(
		"poolName",
		runserver.DefaultPoolName,
		"Name of the InferencePool this Endpoint Picker is associated with.")
	poolNamespace = flag.String(
		"poolNamespace",
		runserver.DefaultPoolNamespace,
		"Namespace of the InferencePool this Endpoint Picker is associated with.")
	refreshMetricsInterval = flag.Duration(
		"refreshMetricsInterval",
		runserver.DefaultRefreshMetricsInterval,
		"interval to refresh metrics")
	refreshPrometheusMetricsInterval = flag.Duration(
		"refreshPrometheusMetricsInterval",
		runserver.DefaultRefreshPrometheusMetricsInterval,
		"interval to flush prometheus metrics")
	logVerbosity = flag.Int(
		"v",
		logging.DEFAULT,
		"number for the log level verbosity")
	secureServing = flag.Bool(
		"secureServing",
		runserver.DefaultSecureServing,
		"Enables secure serving. Defaults to true.")
	healthChecking = flag.Bool(
		"healthChecking",
		runserver.DefaultHealthChecking,
		"Enables health checking")
	certPath = flag.String(
		"certPath",
		runserver.DefaultCertPath,
		"The path to the certificate for secure serving. The certificate and private key files "+
			"are assumed to be named tls.crt and tls.key, respectively. If not set, and secureServing is enabled, "+
			"then a self-signed certificate is used.")
	// metric flags
	totalQueuedRequestsMetric = flag.String(
		"totalQueuedRequestsMetric",
		runserver.DefaultTotalQueuedRequestsMetric,
		"Prometheus metric for the number of queued requests.")
	kvCacheUsagePercentageMetric = flag.String(
		"kvCacheUsagePercentageMetric",
		runserver.DefaultKvCacheUsagePercentageMetric,
		"Prometheus metric for the fraction of KV-cache blocks currently in use (from 0 to 1).")
	// LoRA metrics
	loraInfoMetric = flag.String(
		"loraInfoMetric",
		runserver.DefaultLoraInfoMetric,
		"Prometheus metric for the LoRA info metrics (must be in vLLM label format).")
	// configuration flags
	configFile = flag.String(
		"configFile",
		runserver.DefaultConfigFile,
		"The path to the configuration file")
	configText = flag.String(
		"configText",
		runserver.DefaultConfigText,
		"The configuration specified as text, in lieu of a file")

	modelServerMetricsPort = flag.Int("modelServerMetricsPort", 0, "Port to scrape metrics from pods. "+
		"Default value will be set to InferencePool.Spec.TargetPortNumber if not set.")
	modelServerMetricsPath = flag.String("modelServerMetricsPath", "/metrics", "Path to scrape metrics from pods")

	setupLog = ctrl.Log.WithName("setup")
)

// NewRunner initializes a new EPP Runner and returns its pointer.
func NewRunner() *Runner {
	return &Runner{
		requestControlConfig: requestcontrol.NewConfig(), // default requestcontrol config has empty plugin list
	}
}

// Runner is used to run epp with its plugins
type Runner struct {
	requestControlConfig *requestcontrol.Config
	schedulerConfig      *scheduling.SchedulerConfig
}

func (r *Runner) WithRequestControlConfig(requestControlConfig *requestcontrol.Config) *Runner {
	r.requestControlConfig = requestControlConfig
	return r
}

func (r *Runner) WithSchedulerConfig(schedulerConfig *scheduling.SchedulerConfig) *Runner {
	r.schedulerConfig = schedulerConfig
	return r
}

func bindEnvToFlags() {
	// map[ENV_VAR]flagName   – add more as needed
	for env, flg := range map[string]string{
		"GRPC_PORT":                     "grpcPort",
		"GRPC_HEALTH_PORT":              "grpcHealthPort",
		"MODEL_SERVER_METRICS_PORT":     "modelServerMetricsPort",
		"MODEL_SERVER_METRICS_PATH":     "modelServerMetricsPath",
		"DESTINATION_ENDPOINT_HINT_KEY": "destinationEndpointHintKey",
		"POOL_NAME":                     "poolName",
		"POOL_NAMESPACE":                "poolNamespace",
		// durations & bools work too; flag.Set expects the *string* form
		"REFRESH_METRICS_INTERVAL": "refreshMetricsInterval",
		"SECURE_SERVING":           "secureServing",
	} {
		if v := os.Getenv(env); v != "" {
			// ignore error; Parse() will catch invalid values later
			_ = flag.Set(flg, v)
		}
	}
}

func (r *Runner) Run(ctx context.Context) error {
	// Defaults already baked into flag declarations
	// Load env vars as "soft" overrides
	bindEnvToFlags()

	opts := zap.Options{
		Development: true,
	}
	opts.BindFlags(flag.CommandLine)
	flag.Parse()
	initLogging(&opts)

	// Validate flags
	if err := validateFlags(); err != nil {
		setupLog.Error(err, "Failed to validate flags")
		return err
	}

	// Print all flag values
	flags := make(map[string]any)
	flag.VisitAll(func(f *flag.Flag) {
		flags[f.Name] = f.Value
	})
	setupLog.Info("Flags processed", "flags", flags)

	// --- Load Configurations from Environment Variables ---
	sdConfig := saturationdetector.LoadConfigFromEnv()

	// --- Get Kubernetes Config ---
	cfg, err := ctrl.GetConfig()
	if err != nil {
		setupLog.Error(err, "Failed to get Kubernetes rest config")
		return err
	}

	// --- Setup Datastore ---
	mapping, err := backendmetrics.NewMetricMapping(
		*totalQueuedRequestsMetric,
		*kvCacheUsagePercentageMetric,
		*loraInfoMetric,
	)
	if err != nil {
		setupLog.Error(err, "Failed to create metric mapping from flags.")
		return err
	}
	verifyMetricMapping(*mapping, setupLog)
	pmf := backendmetrics.NewPodMetricsFactory(&backendmetrics.PodMetricsClientImpl{
		MetricMapping:          mapping,
		ModelServerMetricsPort: int32(*modelServerMetricsPort),
		ModelServerMetricsPath: *modelServerMetricsPath,
	}, *refreshMetricsInterval)

	datastore := datastore.NewDatastore(ctx, pmf)

	// --- Setup Metrics Server ---
	customCollectors := []prometheus.Collector{collectors.NewInferencePoolMetricsCollector(datastore)}
	metrics.Register(customCollectors...)
	metrics.RecordInferenceExtensionInfo()
	// Register metrics handler.
	// Metrics endpoint is enabled in 'config/default/kustomization.yaml'. The Metrics options configure the server.
	// More info:
	// - https://pkg.go.dev/sigs.k8s.io/controller-runtime@v0.19.1/pkg/metrics/server
	// - https://book.kubebuilder.io/reference/metrics.html
	metricsServerOptions := metricsserver.Options{
		BindAddress:    fmt.Sprintf(":%d", *metricsPort),
		FilterProvider: filters.WithAuthenticationAndAuthorization,
	}

	poolNamespacedName := types.NamespacedName{
		Name:      *poolName,
		Namespace: *poolNamespace,
	}
	mgr, err := runserver.NewDefaultManager(poolNamespacedName, cfg, metricsServerOptions)
	if err != nil {
		setupLog.Error(err, "Failed to create controller manager")
		return err
	}

	if *enablePprof {
		setupLog.Info("Enabling pprof handlers")
		err = setupPprofHandlers(mgr)
		if err != nil {
			setupLog.Error(err, "Failed to setup pprof handlers")
			return err
		}
	}

	err = r.parsePluginsConfiguration(ctx)
	if err != nil {
		setupLog.Error(err, "Failed to parse plugins configuration")
		return err
	}

	// --- Initialize Core EPP Components ---
	scheduler := scheduling.NewSchedulerWithConfig(r.schedulerConfig)

	saturationDetector := saturationdetector.NewDetector(sdConfig, datastore, setupLog)

	director := requestcontrol.NewDirectorWithConfig(datastore, scheduler, saturationDetector, r.requestControlConfig)

	// --- Setup ExtProc Server Runner ---
	serverRunner := &runserver.ExtProcServerRunner{
		GrpcPort:                                 *grpcPort,
		DestinationEndpointHintMetadataNamespace: *destinationEndpointHintMetadataNamespace,
		DestinationEndpointHintKey:               *destinationEndpointHintKey,
		PoolNamespacedName:                       poolNamespacedName,
		Datastore:                                datastore,
		SecureServing:                            *secureServing,
		HealthChecking:                           *healthChecking,
		CertPath:                                 *certPath,
		RefreshPrometheusMetricsInterval:         *refreshPrometheusMetricsInterval,
		Director:                                 director,
		SaturationDetector:                       saturationDetector,
	}
	if err := serverRunner.SetupWithManager(ctx, mgr); err != nil {
		setupLog.Error(err, "Failed to setup EPP controllers")
		return err
	}

	// --- Add Runnables to Manager ---
	// Register health server.
	if err := registerHealthServer(mgr, ctrl.Log.WithName("health"), datastore, *grpcHealthPort); err != nil {
		return err
	}

	// Register ext-proc server.
	if err := registerExtProcServer(mgr, serverRunner, ctrl.Log.WithName("ext-proc")); err != nil {
		return err
	}

	// --- Start Manager ---
	// This blocks until a signal is received.
	setupLog.Info("Controller manager starting")
	if err := mgr.Start(ctx); err != nil {
		setupLog.Error(err, "Error starting controller manager")
		return err
	}
	setupLog.Info("Controller manager terminated")
	return nil
}

func (r *Runner) parsePluginsConfiguration(ctx context.Context) error {
	handle := newEppHandle(ctx)

	// If no configuration is provided, use the default configuration via config API
	if *configText == "" && *configFile == "" {
		setupLog.Info("No configuration file provided, using default scheduler configuration")
		var err error
		r.schedulerConfig, err = loader.LoadDefaultSchedulerConfig(handle)
		if err != nil {
			return fmt.Errorf("failed to load default scheduler configuration - %w", err)
		}

		// Add requestControl plugins
		r.requestControlConfig.AddPlugins(handle.GetAllPlugins()...)

		log.FromContext(ctx).Info("loaded default configuration successfully")
		return nil
	}

	var configBytes []byte
	if *configText != "" {
		configBytes = []byte(*configText)
	} else if *configFile != "" { // if config was specified through a file
		var err error
		configBytes, err = os.ReadFile(*configFile)
		if err != nil {
			return fmt.Errorf("failed to load config from a file '%s' - %w", *configFile, err)
		}
	}

	config, err := loader.LoadConfig(configBytes, handle)
	if err != nil {
		return fmt.Errorf("failed to load the configuration - %w", err)
	}

	setupLog.Info("Configuration file loaded", "config", config)

	r.schedulerConfig, err = loader.LoadSchedulerConfig(config.SchedulingProfiles, handle)
	if err != nil {
		return fmt.Errorf("failed to create Scheduler configuration - %w", err)
	}

	// Add requestControl plugins
	r.requestControlConfig.AddPlugins(handle.GetAllPlugins()...)

	log.FromContext(ctx).Info("loaded configuration from file/text successfully")
	return nil
}

func initLogging(opts *zap.Options) {
	// Unless -zap-log-level is explicitly set, use -v
	useV := true
	flag.Visit(func(f *flag.Flag) {
		if f.Name == "zap-log-level" {
			useV = false
		}
	})
	if useV {
		// See https://pkg.go.dev/sigs.k8s.io/controller-runtime/pkg/log/zap#Options.Level
		lvl := -1 * (*logVerbosity)
		opts.Level = uberzap.NewAtomicLevelAt(zapcore.Level(int8(lvl)))
	}

	logger := zap.New(zap.UseFlagOptions(opts), zap.RawZapOpts(uberzap.AddCaller()))
	ctrl.SetLogger(logger)
}

// registerExtProcServer adds the ExtProcServerRunner as a Runnable to the manager.
func registerExtProcServer(mgr manager.Manager, runner *runserver.ExtProcServerRunner, logger logr.Logger) error {
	if err := mgr.Add(runner.AsRunnable(logger)); err != nil {
		setupLog.Error(err, "Failed to register ext-proc gRPC server runnable")
		return err
	}
	setupLog.Info("ExtProc server runner added to manager.")
	return nil
}

// registerHealthServer adds the Health gRPC server as a Runnable to the given manager.
func registerHealthServer(mgr manager.Manager, logger logr.Logger, ds datastore.Datastore, port int) error {
	srv := grpc.NewServer()
	healthPb.RegisterHealthServer(srv, &healthServer{
		logger:    logger,
		datastore: ds,
	})
	if err := mgr.Add(
		runnable.NoLeaderElection(runnable.GRPCServer("health", srv, port))); err != nil {
		setupLog.Error(err, "Failed to register health server")
		return err
	}
	return nil
}

func validateFlags() error {
	if *poolName == "" {
		return fmt.Errorf("required %q flag not set", "poolName")
	}
	if *configText != "" && *configFile != "" {
		return fmt.Errorf("both the %q and %q flags can not be set at the same time", "configText", "configFile")
	}

	return nil
}

func verifyMetricMapping(mapping backendmetrics.MetricMapping, logger logr.Logger) {
	if mapping.TotalQueuedRequests == nil {
		logger.Info("Not scraping metric: TotalQueuedRequests")
	}
	if mapping.KVCacheUtilization == nil {
		logger.Info("Not scraping metric: KVCacheUtilization")
	}
	if mapping.LoraRequestInfo == nil {
		logger.Info("Not scraping metric: LoraRequestInfo")
	}
}

// setupPprofHandlers only implements the pre-defined profiles:
// https://cs.opensource.google/go/go/+/refs/tags/go1.24.4:src/runtime/pprof/pprof.go;l=108
func setupPprofHandlers(mgr ctrl.Manager) error {
	var err error
	profiles := []string{
		"heap",
		"goroutine",
		"allocs",
		"threadcreate",
		"block",
		"mutex",
	}
	for _, p := range profiles {
		err = mgr.AddMetricsServerExtraHandler("/debug/pprof/"+p, pprof.Handler(p))
		if err != nil {
			return err
		}
	}
	return nil
}
